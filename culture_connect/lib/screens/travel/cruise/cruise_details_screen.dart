import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';

import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/screens/payment/production_payment_screen.dart';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/common/animated_section.dart';
import 'package:culture_connect/widgets/common/image_gallery.dart';
import 'package:culture_connect/widgets/common/rating_display.dart';
import 'package:culture_connect/widgets/travel/cabin_comparison.dart';
import 'package:culture_connect/widgets/travel/share_travel_service_dialog.dart';

/// Screen for displaying cruise details with animations and additional features
class CruiseDetailsScreen extends ConsumerStatefulWidget {
  /// The cruise to display
  final Cruise cruise;

  /// Creates a new cruise details screen
  const CruiseDetailsScreen({
    super.key,
    required this.cruise,
  });

  @override
  ConsumerState<CruiseDetailsScreen> createState() =>
      _CruiseDetailsScreenState();
}

class _CruiseDetailsScreenState extends ConsumerState<CruiseDetailsScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  int _selectedCabinIndex = 0;
  int _passengerCount = 2;
  double _totalPrice = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  // Services
  final LoggingService _loggingService = LoggingService();

  @override
  void initState() {
    super.initState();
    _calculateTotalPrice();

    // Initialize animation controller
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );

    // Start the animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _calculateTotalPrice() {
    if (widget.cruise.cabins.isNotEmpty) {
      _totalPrice = widget.cruise.cabins[_selectedCabinIndex].pricePerPerson *
          _passengerCount;
    } else {
      _totalPrice = widget.cruise.price * _passengerCount;
    }
  }

  void _showCabinComparisonDialog() {
    try {
      showDialog(
        context: context,
        builder: (context) => CabinComparisonDialog(
          cabins: widget.cruise.cabins,
          onCabinSelected: (cabin) {
            if (!mounted) return;
            setState(() {
              _selectedCabinIndex =
                  widget.cruise.cabins.indexWhere((c) => c.id == cabin.id);
              _calculateTotalPrice();
            });
          },
        ),
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'CruiseDetailsScreenEnhanced',
        'Error showing cabin comparison dialog',
        e,
        stackTrace,
      );
    }
  }

  void _showShareDialog() {
    try {
      showDialog(
        context: context,
        builder: (context) => ShareTravelServiceDialog(
          travelService: widget.cruise,
        ),
      );
    } catch (e, stackTrace) {
      _loggingService.error(
        'CruiseDetailsScreenEnhanced',
        'Error showing share dialog',
        e,
        stackTrace,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // App bar with image
            SliverAppBar(
              expandedHeight: 250,
              pinned: true,
              actions: [
                IconButton(
                  icon: const Icon(Icons.share),
                  onPressed: _showShareDialog,
                  tooltip: 'Share this cruise',
                ),
                IconButton(
                  icon: const Icon(Icons.favorite_border),
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Added to favorites'),
                      ),
                    );
                  },
                  tooltip: 'Add to favorites',
                ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Hero(
                  tag: 'cruise_image_${widget.cruise.id}',
                  child: Image.network(
                    widget.cruise.imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      _loggingService.error(
                        'CruiseDetailsScreenEnhanced',
                        'Error loading cruise image',
                        error,
                        stackTrace,
                      );
                      return Container(
                        color: theme.colorScheme.surfaceContainerHighest,
                        child: Center(
                          child: Icon(
                            Icons.image_not_supported,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ),

            // Content
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Title and rating
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.cruise.name,
                                style: theme.textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 4),
                              Text(
                                '${widget.cruise.cruiseLine} - ${widget.cruise.shipName}',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            RatingDisplay(
                              rating: widget.cruise.rating,
                              size: 20,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${widget.cruise.reviewCount} reviews',
                              style: theme.textTheme.bodySmall,
                            ),
                          ],
                        ),
                      ],
                    ),

                    SizedBox(height: 16),

                    // Price
                    Row(
                      children: [
                        if (widget.cruise.isOnSale &&
                            widget.cruise.originalPrice != null) ...[
                          Text(
                            widget.cruise.formattedOriginalPrice!,
                            style: theme.textTheme.titleMedium?.copyWith(
                              decoration: TextDecoration.lineThrough,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                          SizedBox(width: 8),
                        ],
                        Text(
                          '${widget.cruise.formattedPrice} / person',
                          style: theme.textTheme.titleLarge?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),

                    SizedBox(height: 24),

                    // Cruise Overview
                    AnimatedSection(
                      title: 'Cruise Overview',
                      content: Card(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            children: [
                              _buildInfoRow(
                                Icons.directions_boat,
                                'Type',
                                widget.cruise.cruiseType.displayName,
                              ),
                              const Divider(),
                              _buildInfoRow(
                                Icons.calendar_today,
                                'Duration',
                                widget.cruise.formattedDuration,
                              ),
                              const Divider(),
                              _buildInfoRow(
                                Icons.location_on,
                                'Departure',
                                '${widget.cruise.departurePort}, ${DateFormat('dd/MM/yyyy').format(widget.cruise.departureDateTime)}',
                              ),
                              const Divider(),
                              _buildInfoRow(
                                Icons.location_on,
                                'Return',
                                '${widget.cruise.arrivalPort}, ${DateFormat('dd/MM/yyyy').format(widget.cruise.arrivalDateTime)}',
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 16),

                    // Ship Information
                    AnimatedSection(
                      title: 'Ship Information',
                      content: Card(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                widget.cruise.shipName,
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8),
                              Text(
                                widget.cruise.shipDescription,
                                style: theme.textTheme.bodyMedium,
                              ),
                              SizedBox(height: 16),
                              _buildShipDetailsGrid(),
                            ],
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 16),

                    // Photo Gallery
                    AnimatedSection(
                      title: 'Photo Gallery',
                      content: ImageGallery(
                        images: [
                          widget.cruise.imageUrl,
                          ...widget.cruise.additionalImages
                        ],
                        height: 200,
                      ),
                    ),

                    SizedBox(height: 16),

                    // Cabin Selection
                    AnimatedSection(
                      title: 'Select Cabin',
                      content: Column(
                        children: [
                          _buildCabinSelection(),
                          SizedBox(height: 16),
                          if (widget.cruise.cabins.length > 1)
                            SizedBox(
                              width: double.infinity,
                              child: OutlinedButton.icon(
                                onPressed: _showCabinComparisonDialog,
                                icon: const Icon(Icons.compare_arrows),
                                label: const Text('Compare Cabins'),
                              ),
                            ),
                        ],
                      ),
                    ),

                    SizedBox(height: 16),

                    // Passenger Count
                    AnimatedSection(
                      title: 'Number of Passengers',
                      content: Card(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              IconButton(
                                onPressed: _passengerCount > 1
                                    ? () {
                                        if (!mounted) return;
                                        setState(() {
                                          _passengerCount--;
                                          _calculateTotalPrice();
                                        });
                                      }
                                    : null,
                                icon: const Icon(Icons.remove_circle_outline),
                                color: _passengerCount > 1
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.onSurface.withAlpha(77),
                              ),
                              SizedBox(width: 16),
                              Text(
                                '$_passengerCount',
                                style: theme.textTheme.headlineSmall,
                              ),
                              SizedBox(width: 16),
                              IconButton(
                                onPressed: _passengerCount < 10
                                    ? () {
                                        if (!mounted) return;
                                        setState(() {
                                          _passengerCount++;
                                          _calculateTotalPrice();
                                        });
                                      }
                                    : null,
                                icon: const Icon(Icons.add_circle_outline),
                                color: _passengerCount < 10
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.onSurface.withAlpha(77),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 16),

                    // Amenities
                    AnimatedSection(
                      title: 'Onboard Amenities',
                      content: _buildAmenitiesGrid(),
                    ),

                    SizedBox(height: 16),

                    // Description
                    AnimatedSection(
                      title: 'About this Cruise',
                      content: Card(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Text(
                            widget.cruise.description,
                            style: theme.textTheme.bodyMedium,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 16),

                    // Cancellation Policy
                    AnimatedSection(
                      title: 'Cancellation Policy',
                      content: Card(
                        child: Padding(
                          padding: EdgeInsets.all(16),
                          child: Text(
                            widget.cruise.cancellationPolicy,
                            style: theme.textTheme.bodyMedium,
                          ),
                        ),
                      ),
                    ),

                    SizedBox(height: 100), // Space for the bottom button
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      bottomSheet: Container(
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(26),
              blurRadius: 10,
              offset: const Offset(0, -5),
            ),
          ],
        ),
        child: SafeArea(
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                try {
                  // Navigate to payment screen
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => PaymentScreen(
                        amount: _totalPrice,
                        currency: widget.cruise.currency,
                        description:
                            'Cruise: ${widget.cruise.name} (${widget.cruise.durationDays} days, $_passengerCount passengers)',
                      ),
                    ),
                  );
                } catch (e, stackTrace) {
                  _loggingService.error(
                    'CruiseDetailsScreenEnhanced',
                    'Error navigating to payment screen',
                    e,
                    stackTrace,
                  );

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                          'Failed to navigate to payment screen. Please try again.'),
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 16),
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
              ),
              child: Text(
                'Book Now - ${widget.cruise.currency}${_totalPrice.toStringAsFixed(2)}',
                style: theme.textTheme.titleMedium?.copyWith(
                  color: theme.colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    final theme = Theme.of(context);

    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            size: 24,
            color: theme.colorScheme.primary,
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShipDetailsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 3,
      children: [
        _buildDetailItem(
            Icons.calendar_today, 'Built', widget.cruise.yearBuilt.toString()),
        if (widget.cruise.yearRefurbished != null)
          _buildDetailItem(Icons.update, 'Refurbished',
              widget.cruise.yearRefurbished.toString()),
        _buildDetailItem(Icons.people, 'Passengers',
            widget.cruise.passengerCount.toString()),
        _buildDetailItem(
            Icons.person, 'Crew', widget.cruise.crewCount.toString()),
        _buildDetailItem(
            Icons.layers, 'Decks', widget.cruise.deckCount.toString()),
        _buildDetailItem(
            Icons.straighten, 'Length', '${widget.cruise.shipLength}m'),
        _buildDetailItem(
            Icons.swap_horiz, 'Width', '${widget.cruise.shipWidth}m'),
        _buildDetailItem(
            Icons.scale, 'Tonnage', '${widget.cruise.shipTonnage} tons'),
      ],
    );
  }

  Widget _buildDetailItem(IconData icon, String title, String value) {
    final theme = Theme.of(context);

    return Card(
      child: Padding(
        padding: EdgeInsets.all(8),
        child: Row(
          children: [
            Icon(
              icon,
              size: 24,
              color: theme.colorScheme.primary,
            ),
            SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCabinSelection() {
    final theme = Theme.of(context);

    if (widget.cruise.cabins.isEmpty) {
      return Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text(
            'No cabin information available',
            style: theme.textTheme.bodyMedium,
          ),
        ),
      );
    }

    return Column(
      children: List.generate(
        widget.cruise.cabins.length,
        (index) {
          final cabin = widget.cruise.cabins[index];
          final isSelected = _selectedCabinIndex == index;

          return Card(
            margin: EdgeInsets.only(bottom: 8),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
              side: BorderSide(
                color:
                    isSelected ? theme.colorScheme.primary : Colors.transparent,
                width: 2,
              ),
            ),
            child: InkWell(
              onTap: () {
                if (!mounted) return;
                setState(() {
                  _selectedCabinIndex = index;
                  _calculateTotalPrice();
                });
              },
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Cabin image
                    ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        cabin.imageUrl,
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          _loggingService.error(
                            'CruiseDetailsScreenEnhanced',
                            'Error loading cabin image',
                            error,
                            stackTrace,
                          );
                          return Container(
                            width: 80,
                            height: 80,
                            color: theme.colorScheme.surfaceContainerHighest,
                            child: Icon(
                              Icons.image_not_supported,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          );
                        },
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            cabin.type.displayName,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            cabin.description,
                            style: theme.textTheme.bodySmall,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 8),
                          Row(
                            children: [
                              Icon(
                                Icons.straighten,
                                size: 16,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              SizedBox(width: 4),
                              Text(
                                cabin.size,
                                style: theme.textTheme.bodySmall,
                              ),
                              SizedBox(width: 16),
                              Icon(
                                Icons.people,
                                size: 16,
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                              SizedBox(width: 4),
                              Text(
                                'Max ${cabin.maxGuests} guests',
                                style: theme.textTheme.bodySmall,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          cabin.formattedPricePerPerson,
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'per person',
                          style: theme.textTheme.bodySmall,
                        ),
                        SizedBox(height: 16),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: theme.colorScheme.primary,
                            size: 24,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAmenitiesGrid() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        if (widget.cruise.hasPool) _buildAmenityChip(Icons.pool, 'Pool'),
        if (widget.cruise.hasSpa) _buildAmenityChip(Icons.spa, 'Spa'),
        if (widget.cruise.hasGym)
          _buildAmenityChip(Icons.fitness_center, 'Gym'),
        if (widget.cruise.hasKidsClub)
          _buildAmenityChip(Icons.child_care, 'Kids Club'),
        if (widget.cruise.hasCasino) _buildAmenityChip(Icons.casino, 'Casino'),
        if (widget.cruise.hasEntertainment)
          _buildAmenityChip(Icons.theater_comedy, 'Entertainment'),
        if (widget.cruise.hasWifi) _buildAmenityChip(Icons.wifi, 'WiFi'),
        if (widget.cruise.hasRestaurants)
          _buildAmenityChip(Icons.restaurant, 'Restaurants'),
        if (widget.cruise.hasBars) _buildAmenityChip(Icons.local_bar, 'Bars'),
        if (widget.cruise.hasRoomService)
          _buildAmenityChip(Icons.room_service, 'Room Service'),
        if (widget.cruise.hasMedicalCenter)
          _buildAmenityChip(Icons.local_hospital, 'Medical Center'),
      ],
    );
  }

  Widget _buildAmenityChip(IconData icon, String label) {
    final theme = Theme.of(context);

    return Chip(
      avatar: Icon(
        icon,
        size: 16,
        color: theme.colorScheme.primary,
      ),
      label: Text(
        label,
        style: theme.textTheme.bodySmall,
      ),
      backgroundColor: theme.colorScheme.surfaceContainerHighest,
    );
  }
}
