import 'package:flutter/material.dart';
import 'package:culture_connect/models/travel/transfer/transfers.dart'
    as models;
import 'package:culture_connect/models/travel/transfer/transfer_extensions.dart';
import 'package:culture_connect/screens/travel/transfer/transfer_bookings_screen.dart';
import 'package:culture_connect/widgets/common/animated_check.dart';
import 'package:share_plus/share_plus.dart';

// Type aliases to make the code more readable
typedef TransferBooking = models.TransferBooking;
typedef TransferBookingStatus = models.TransferBookingStatus;

/// A screen for displaying booking confirmation
class TransferBookingConfirmationScreen extends StatelessWidget {
  /// The booking to display
  final TransferBooking booking;

  /// Creates a new transfer booking confirmation screen
  const TransferBookingConfirmationScreen({
    super.key,
    required this.booking,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Booking Confirmed'),
        automaticallyImplyLeading: false,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(height: 24),
            Animated<PERSON>he<PERSON>(
              size: 100,
              color: theme.colorScheme.primary,
            ),
            const SizedBox(height: 24),
            Text(
              'Booking Confirmed!',
              style: theme.textThemeeadlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Your transfer has been successfully booked.',
              style: theme.textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            _buildConfirmationDetails(context),
            const SizedBox(height: 24),
            _buildInsuranceRecommendation(context),
            const SizedBox(height: 24),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  /// Build the confirmation details
  Widget _buildConfirmationDetails(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Booking Details',
              style: theme.textTheme.titleLarge,
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
              context,
              title: 'Booking Reference',
              value: booking.confirmationCode ?? 'Pending',
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              title: 'Status',
              value: booking.status.displayName,
              valueColor: booking.status.color,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              title: 'Transfer',
              value: booking.transferService?.name ?? 'Airport Transfer',
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              title: 'Pickup Date',
              value: booking.formattedPickupDate,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              title: 'Pickup Time',
              value: booking.formattedPickupTime,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              title: 'Pickup Location',
              value: booking.pickupLocation.name,
              subtitle: booking.pickupLocation.address,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              title: 'Dropoff Location',
              value: booking.dropoffLocation.name,
              subtitle: booking.dropoffLocation.address,
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              title: 'Passengers',
              value: booking.passengerCount.toString(),
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              title: 'Luggage',
              value: booking.luggageCount.toString(),
            ),
            if (booking.flightInfo != null) ...[
              const SizedBox(height: 12),
              _buildDetailRow(
                context,
                title: 'Flight',
                value: booking.flightInfo!,
              ),
            ],
            const SizedBox(height: 16),
            const Divider(),
            const SizedBox(height: 16),
            _buildDetailRow(
              context,
              title: 'Total Paid',
              value: booking.formattedTotalPrice,
              valueStyle: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 12),
            _buildDetailRow(
              context,
              title: 'Payment Method',
              value: booking.paymentMethod ?? 'Unknown',
            ),
          ],
        ),
      ),
    );
  }

  /// Build insurance recommendation card
  Widget _buildInsuranceRecommendation(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.shield,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  'Protect Your Journey',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Consider adding travel insurance to protect your transfer and trip. Get coverage for cancellations, delays, and unexpected events.',
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      // Navigate to insurance screen
                      Navigator.pushNamed(context, '/travel/insurance');
                    },
                    child: const Text('Learn More'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      // Navigate to insurance search with transfer details
                      Navigator.pushNamed(
                        context,
                        '/travel/insurance/search',
                        arguments: {
                          'destination': booking.dropoffLocation.name,
                          'startDate': booking.pickupDateTime,
                          'endDate': booking.pickupDateTime
                              .add(const Duration(days: 1)),
                          'travelerCount': booking.passengerCount,
                        },
                      );
                    },
                    child: const Text('Get Quote'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build the action buttons
  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: () {
              _shareBooking();
            },
            icon: const Icon(Icons.share),
            label: const Text('Share Booking Details'),
            style: ElevatedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: () {
              Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                  builder: (context) => const TransferBookingsScreen(),
                ),
              );
            },
            icon: const Icon(Iconsistory),
            label: const Text('View My Bookings'),
            style: OutlinedButton.styleFrom(
              padding: EdgeInsets.symmetric(vertical: 16),
            ),
          ),
        ),
        const SizedBox(height: 16),
        TextButton(
          onPressed: () {
            Navigator.popUntil(context, (route) => route.isFirst);
          },
          child: const Text('Back to Home'),
        ),
      ],
    );
  }

  /// Build a detail row
  Widget _buildDetailRow(
    BuildContext context, {
    required String title,
    required String value,
    String? subtitle,
    Color? valueColor,
    TextStyle? valueStyle,
  }) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(
          width: 120,
          child: Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: valueStyle ??
                    theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: valueColor,
                    ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: theme.textTheme.bodySmall,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  /// Share the booking
  void _shareBooking() {
    final text = 'Booking Confirmation\n\n'
        'Reference: ${booking.confirmationCode ?? 'Pending'}\n'
        'Transfer: ${booking.transferService?.name ?? 'Airport Transfer'}\n'
        'Pickup: ${booking.formattedPickupDateTime}\n'
        'From: ${booking.pickupLocation.name}\n'
        'To: ${booking.dropoffLocation.name}\n'
        'Passengers: ${booking.passengerCount}\n'
        'Luggage: ${booking.luggageCount}\n'
        'Total: ${booking.formattedTotalPrice}\n';

    Share.share(text, subject: 'Transfer Booking Confirmation');
  }
}
