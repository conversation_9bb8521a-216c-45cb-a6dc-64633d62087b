import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/voice_translation_model.dart';
import 'package:culture_connect/providers/voice_translation_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/voice_translation/audio_player_controls.dart';

/// A widget for displaying translated content
class TranslatedContentDisplay extends ConsumerWidget {
  /// Creates a new translated content display
  const TranslatedContentDisplay({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final translation = ref.watch(currentVoiceTranslationProvider);
    final translationAsync = ref.watch(voiceTranslationNotifierProvider);

    if (translation == null) {
      return const SizedBox.shrink();
    }

    return translationAsync.when(
      data: (_) => _buildContent(context, ref, translation),
      loading: () => _buildLoadingState(translation),
      error: (error, stackTrace) =>
          _buildErrorState(context, ref, error.toString()),
    );
  }

  Widget _buildContent(
      BuildContext context, WidgetRef ref, VoiceTranslationModel translation) {
    switch (translation.status) {
      case VoiceTranslationStatus.initial:
        return _buildInitialState();
      case VoiceTranslationStatus.recording:
        return _buildRecordingState();
      case VoiceTranslationStatus.processing:
        return _buildProcessingState();
      case VoiceTranslationStatus.completed:
        return _buildCompletedState(context, ref, translation);
      case VoiceTranslationStatus.error:
        return _buildErrorState(context, ref, translation.errorMessage);
    }
  }

  Widget _buildInitialState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mic,
            size: 48,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            'Tap the microphone button to start recording',
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecordingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mic,
            size: 48,
            color: Colors.red,
          ),
          SizedBox(height: 16),
          Text(
            'Recording...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.red,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Speak clearly into the microphone',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProcessingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 48,
            height: 48,
            child: CircularProgressIndicator(),
          ),
          SizedBox(height: 16),
          Text(
            'Processing...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Translating your speech',
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(VoiceTranslationModel translation) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(
            width: 48,
            height: 48,
            child: CircularProgressIndicator(),
          ),
          const SizedBox(height: 16),
          Text(
            translation.status == VoiceTranslationStatus.recording
                ? 'Recording...'
                : translation.status == VoiceTranslationStatus.processing
                    ? 'Processing...'
                    : 'Loading...',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompletedState(
      BuildContext context, WidgetRef ref, VoiceTranslationModel translation) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Original text
          _buildTextSection(
            context,
            'Original (${translation.getSourceLanguageName()})',
            translation.originalText ?? '',
            translation.originalAudioPath != null,
            () => ref
                .read(voiceTranslationNotifierProvider.notifier)
                .playOriginalAudio(),
          ),

          const SizedBox(height: 24),

          // Translated text
          _buildTextSection(
            context,
            'Translation (${translation.getTargetLanguageName()})',
            translation.translatedText ?? '',
            translation.translatedAudioPath != null,
            () => ref
                .read(voiceTranslationNotifierProvider.notifier)
                .playTranslatedAudio(),
          ),

          const SizedBox(height: 24),

          // Audio player controls
          if (translation.originalAudioPath != null ||
              translation.translatedAudioPath != null)
            const AudioPlayerControls(),

          const SizedBox(height: 24),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildActionButton(
                context,
                Icons.favorite,
                translation.isFavorite ? Colors.red : Colors.grey,
                translation.isFavorite
                    ? 'Remove from favorites'
                    : 'Add to favorites',
                () => ref
                    ead(voiceTranslationNotifierProvider.notifier)
                    .toggleFavorite(),
              ),
              _buildActionButton(
                context,
                Icons.share,
                Colors.blue,
                'Share translation',
                () => _shareTranslation(context, translation),
              ),
              _buildActionButton(
                context,
                Icons.delete,
                Colorsed,
                'Delete translation',
                () => _confirmDelete(context, ref),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTextSection(
    BuildContext context,
    String title,
    String text,
    bool hasAudio,
    VoidCallback onPlayPressed,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Row(
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimaryColor,
              ),
            ),
            const Spacer(),
            if (hasAudio)
              IconButton(
                icon: const Icon(Icons.volume_up),
                onPressed: onPlayPressed,
                tooltip: 'Play audio',
                color: AppTheme.primaryColor,
              ),
          ],
        ),

        const SizedBox(height: 8),

        // Text content
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Colors.grey[300]!,
              width: 1,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                text,
                style: const TextStyle(
                  fontSize: 16,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(height: 8),
              Align(
                alignment: Alignment.centerRight,
                child: IconButton(
                  icon: const Icon(Icons.copy),
                  onPressed: () => _copyToClipboard(context, text),
                  tooltip: 'Copy to clipboard',
                  iconSize: 20,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    IconData icon,
    Color color,
    String tooltip,
    VoidCallback onPressed,
  ) {
    return Tooltip(
      message: tooltip,
      child: IconButton(
        icon: Icon(icon),
        onPressed: onPressed,
        color: color,
        iconSize: 24,
      ),
    );
  }

  Widget _buildErrorState(
      BuildContext context, WidgetRef ref, String? errorMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 48,
            color: Colorsed,
          ),
          const SizedBox(height: 16),
          const Text(
            'Error',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colorsed,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            errorMessage ?? 'An error occurred during translation',
            style: const TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              // Reset the translation
              refead(voiceTranslationNotifierProvider.notifier)eset();
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  void _copyToClipboard(BuildContext context, String text) {
    Clipboard.setData(ClipboardData(text: text));
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Copied to clipboard'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _shareTranslation(
      BuildContext context, VoiceTranslationModel translation) {
    // In a real app, this would use the share plugin
    final text = '''
Original (${translation.getSourceLanguageName()}):
${translation.originalText}

Translation (${translation.getTargetLanguageName()}):
${translation.translatedText}
''';

    Clipboard.setData(ClipboardData(text: text));
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Translation copied to clipboard for sharing'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _confirmDelete(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Translation'),
        content:
            const Text('Are you sure you want to delete this translation?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await ref
                  ead(voiceTranslationNotifierProvider.notifier)
                  .deleteTranslation();
              if (context.mounted) {
                Navigator.pop(context);
              }
            },
            child: const Text('Delete', style: TextStyle(color: Colorsed)),
          ),
        ],
      ),
    );
  }
}
